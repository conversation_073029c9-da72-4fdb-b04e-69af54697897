'use client'

import React, { useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'

interface ProtectedRouteProps {
  children: React.ReactNode
  redirectTo?: string
  requireAuth?: boolean
  preventAuthAccess?: boolean
}

/**
 * ProtectedRoute component that handles route protection and redirects
 * 
 * @param children - The content to render if access is allowed
 * @param redirectTo - Where to redirect (default: '/' for auth pages, '/auth' for protected pages)
 * @param requireAuth - If true, requires authentication to access (default: false)
 * @param preventAuthAccess - If true, prevents authenticated users from accessing (default: false)
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  redirectTo,
  requireAuth = false,
  preventAuthAccess = false
}) => {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // Don't redirect while loading
    if (isLoading) return

    // If route requires auth and user is not authenticated
    if (requireAuth && !isAuthenticated) {
      router.replace(redirectTo || '/auth')
      return
    }

    // If route prevents auth access and user is authenticated
    if (preventAuthAccess && isAuthenticated) {
      router.replace(redirectTo || '/')
      return
    }
  }, [isAuthenticated, isLoading, requireAuth, preventAuthAccess, redirectTo, router])

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-900 mx-auto mb-4"></div>
          <p className="text-slate-600 font-medium">Loading...</p>
        </div>
      </div>
    )
  }

  // If route requires auth and user is not authenticated, don't render content
  if (requireAuth && !isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-slate-600 font-medium">Redirecting to login...</p>
        </div>
      </div>
    )
  }

  // If route prevents auth access and user is authenticated, don't render content
  if (preventAuthAccess && isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-slate-600 font-medium">Redirecting...</p>
        </div>
      </div>
    )
  }

  // Render the protected content
  return <>{children}</>
}

/**
 * Convenience wrapper for auth pages that should redirect authenticated users
 */
export const AuthPageWrapper: React.FC<{ children: React.ReactNode; redirectTo?: string }> = ({
  children,
  redirectTo = '/'
}) => {
  return (
    <ProtectedRoute preventAuthAccess={true} redirectTo={redirectTo}>
      {children}
    </ProtectedRoute>
  )
}

/**
 * Convenience wrapper for pages that require authentication
 */
export const RequireAuthWrapper: React.FC<{ children: React.ReactNode; redirectTo?: string }> = ({
  children,
  redirectTo = '/auth'
}) => {
  return (
    <ProtectedRoute requireAuth={true} redirectTo={redirectTo}>
      {children}
    </ProtectedRoute>
  )
}
