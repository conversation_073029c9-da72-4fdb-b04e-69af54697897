'use client'

import React, { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Navigation } from '@/components/ui/Navigation'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { RequireAuthWrapper } from '@/components/auth/ProtectedRoute'

export default function SettingsPage() {
  const { user } = useAuth()
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  const [notifications, setNotifications] = useState({
    emailNotifications: true,
    pushNotifications: false,
    marketingEmails: false
  })

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleNotificationChange = (key: keyof typeof notifications) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key]
    }))
  }

  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // TODO: Implement password change functionality
    console.log('Changing password:', passwordData)
    setPasswordData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
  }

  const handleNotificationSubmit = () => {
    // TODO: Implement notification settings update
    console.log('Updating notifications:', notifications)
  }

  return (
    <RequireAuthWrapper>
      <div className="min-h-screen bg-slate-50">
        <Navigation />
        
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-slate-900 mb-2">Account Settings</h1>
            <p className="text-slate-600">Manage your account security and preferences</p>
          </div>

          {/* Security Settings Card */}
          <Card className="mb-6">
            <CardHeader>
              <h2 className="text-xl font-semibold text-slate-900">Security</h2>
              <p className="text-slate-600 text-sm mt-1">Update your password and security settings</p>
            </CardHeader>
            <CardContent>
              <form onSubmit={handlePasswordSubmit} className="space-y-6">
                <div className="grid grid-cols-1 gap-6">
                  <Input
                    label="Current Password"
                    name="currentPassword"
                    type="password"
                    value={passwordData.currentPassword}
                    onChange={handlePasswordChange}
                    variant="modern"
                    required
                  />
                  <Input
                    label="New Password"
                    name="newPassword"
                    type="password"
                    value={passwordData.newPassword}
                    onChange={handlePasswordChange}
                    variant="modern"
                    required
                  />
                  <Input
                    label="Confirm New Password"
                    name="confirmPassword"
                    type="password"
                    value={passwordData.confirmPassword}
                    onChange={handlePasswordChange}
                    variant="modern"
                    required
                  />
                </div>
                <Button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Update Password
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Notification Settings Card */}
          <Card className="mb-6">
            <CardHeader>
              <h2 className="text-xl font-semibold text-slate-900">Notifications</h2>
              <p className="text-slate-600 text-sm mt-1">Choose what notifications you want to receive</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {Object.entries(notifications).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between py-3 border-b border-slate-100 last:border-b-0">
                    <div>
                      <h3 className="font-medium text-slate-900">
                        {key === 'emailNotifications' && 'Email Notifications'}
                        {key === 'pushNotifications' && 'Push Notifications'}
                        {key === 'marketingEmails' && 'Marketing Emails'}
                      </h3>
                      <p className="text-sm text-slate-600">
                        {key === 'emailNotifications' && 'Receive important updates via email'}
                        {key === 'pushNotifications' && 'Get push notifications on your device'}
                        {key === 'marketingEmails' && 'Receive promotional content and offers'}
                      </p>
                    </div>
                    <button
                      type="button"
                      onClick={() => handleNotificationChange(key as keyof typeof notifications)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                        value ? 'bg-blue-600' : 'bg-slate-200'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          value ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                ))}
                <Button
                  onClick={handleNotificationSubmit}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Save Notification Settings
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Danger Zone Card */}
          <Card className="border-red-200">
            <CardHeader>
              <h2 className="text-xl font-semibold text-red-900">Danger Zone</h2>
              <p className="text-red-600 text-sm mt-1">Irreversible and destructive actions</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                  <h3 className="font-medium text-red-900 mb-2">Delete Account</h3>
                  <p className="text-sm text-red-700 mb-4">
                    Once you delete your account, there is no going back. Please be certain.
                  </p>
                  <Button
                    variant="outline"
                    className="border-red-300 text-red-700 hover:bg-red-50 hover:border-red-400"
                  >
                    Delete Account
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </RequireAuthWrapper>
  )
}
