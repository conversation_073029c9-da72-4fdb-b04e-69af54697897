"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/auth/ProtectedRoute.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleLogout = ()=>{\n        logout();\n        router.push('/');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__.RequireAuthWrapper, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-foreground\",\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted mt-1\",\n                                        children: [\n                                            \"Welcome back, \",\n                                            (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.email) || 'User',\n                                            \"!\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleLogout,\n                                variant: \"outline\",\n                                children: \"Sign Out\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-foreground\",\n                                    children: \"User Information\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-foreground\",\n                                                    children: \"Email:\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-muted\",\n                                                    children: user.email\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 17\n                                        }, this),\n                                        user.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-foreground\",\n                                                    children: \"Name:\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 47,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-muted\",\n                                                    children: user.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-foreground\",\n                                                    children: \"User ID:\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-muted font-mono text-sm\",\n                                                    children: user.id\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 53,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-foreground\",\n                                    children: \"Welcome to MRH\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted mb-4\",\n                                        children: \"You have successfully authenticated! This is a protected dashboard page that requires authentication to access.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted\",\n                                        children: \"The authentication system includes:\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"list-disc list-inside text-muted mt-2 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"Secure JWT-based authentication\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"Password hashing with bcrypt\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"Form validation and error handling\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"Responsive design with lime green theme\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"Protected routes and authentication context\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"mU4omnRdQA8PySeBpGE/lWg2WMg=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});