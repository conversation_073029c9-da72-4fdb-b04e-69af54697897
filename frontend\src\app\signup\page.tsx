'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AuthPageWrapper } from '@/components/auth/ProtectedRoute'

export default function SignUpPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to the main auth page
    router.replace('/auth')
  }, [router])

  return (
    <AuthPageWrapper>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-slate-600 font-medium">Redirecting to sign up...</p>
        </div>
      </div>
    </AuthPageWrapper>
  )
}
