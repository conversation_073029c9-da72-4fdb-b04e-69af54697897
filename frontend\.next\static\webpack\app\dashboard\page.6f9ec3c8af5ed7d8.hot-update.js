"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/QuickActions.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/QuickActions.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuickActions: () => (/* binding */ QuickActions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuickActions auto */ \n\n\n\nconst QuickActions = (param)=>{\n    let { className = '' } = param;\n    const quickActions = [\n        {\n            id: 'add-property',\n            title: 'Add Property',\n            description: 'List a new property',\n            href: '/dashboard/properties/new',\n            color: 'blue',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            id: 'view-analytics',\n            title: 'View Analytics',\n            description: 'Check performance metrics',\n            href: '/dashboard/analytics',\n            color: 'green',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            id: 'messages',\n            title: 'Messages',\n            description: 'Check new messages',\n            href: '/dashboard/messages',\n            color: 'purple',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            id: 'profile',\n            title: 'Update Profile',\n            description: 'Manage your account',\n            href: '/dashboard/profile',\n            color: 'orange',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, undefined)\n        }\n    ];\n    const getColorClasses = (color)=>{\n        const colorMap = {\n            blue: {\n                bg: 'bg-blue-50 hover:bg-blue-100',\n                icon: 'bg-blue-500 text-white',\n                border: 'border-blue-200 hover:border-blue-300'\n            },\n            green: {\n                bg: 'bg-green-50 hover:bg-green-100',\n                icon: 'bg-green-500 text-white',\n                border: 'border-green-200 hover:border-green-300'\n            },\n            purple: {\n                bg: 'bg-purple-50 hover:bg-purple-100',\n                icon: 'bg-purple-500 text-white',\n                border: 'border-purple-200 hover:border-purple-300'\n            },\n            orange: {\n                bg: 'bg-orange-50 hover:bg-orange-100',\n                icon: 'bg-orange-500 text-white',\n                border: 'border-orange-200 hover:border-orange-300'\n            },\n            red: {\n                bg: 'bg-red-50 hover:bg-red-100',\n                icon: 'bg-red-500 text-white',\n                border: 'border-red-200 hover:border-red-300'\n            }\n        };\n        return colorMap[color];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.DashboardCard, {\n        title: \"Quick Actions\",\n        subtitle: \"Common tasks and shortcuts\",\n        className: className,\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n                lineNumber: 125,\n                columnNumber: 11\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n            lineNumber: 124,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-1 2xl:grid-cols-2 gap-4\",\n            children: quickActions.map((action)=>{\n                const colors = getColorClasses(action.color);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: action.href,\n                    className: \"\\n                block p-4 rounded-lg border-2 transition-all duration-200\\n                \".concat(colors.bg, \" \").concat(colors.border, \"\\n                hover:shadow-md hover:scale-105\\n                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\\n              \"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 rounded-lg \".concat(colors.icon),\n                                children: action.icon\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-semibold text-slate-900 mb-1\",\n                                        children: action.title\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-slate-600\",\n                                        children: action.description\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 text-slate-400\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M9 5l7 7-7 7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 15\n                    }, undefined)\n                }, action.id, false, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 13\n                }, undefined);\n            })\n        }, void 0, false, {\n            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n            lineNumber: 129,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\dashboard\\\\QuickActions.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, undefined);\n};\n_c = QuickActions;\nvar _c;\n$RefreshReg$(_c, \"QuickActions\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/QuickActions.tsx\n"));

/***/ })

});